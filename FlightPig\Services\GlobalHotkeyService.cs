using System;
using System.Runtime.InteropServices;
using System.Threading;
using System.Windows.Forms;

namespace FlightPig.Services
{
    /// <summary>
    /// Service for handling global hotkeys that work even when the application doesn't have focus
    /// </summary>
    public class GlobalHotkeyService : IDisposable
    {
        // Windows API constants
        private const int WM_HOTKEY = 0x0312;
        private const int MOD_NONE = 0x0000;
        private const int VK_F1 = 0x70;

        // Windows API imports
        [DllImport("user32.dll")]
        private static extern bool RegisterHotKey(IntPtr hWnd, int id, int fsModifiers, int vk);

        [DllImport("user32.dll")]
        private static extern bool UnregisterHotKey(IntPtr hWnd, int id);

        [DllImport("kernel32.dll")]
        private static extern IntPtr GetConsoleWindow();

        private readonly int _hotkeyId;
        private readonly Thread _messageLoopThread;
        private readonly ManualResetEventSlim _stopEvent;
        private HotkeyForm _form;
        private bool _disposed = false;

        /// <summary>
        /// Event fired when F1 hotkey is pressed
        /// </summary>
        public event EventHandler F1Pressed;

        public GlobalHotkeyService()
        {
            _hotkeyId = 1; // Unique ID for our hotkey
            _stopEvent = new ManualResetEventSlim(false);

            // Start message loop in a separate thread
            _messageLoopThread = new Thread(MessageLoopWorker)
            {
                IsBackground = true,
                Name = "GlobalHotkeyMessageLoop"
            };
            _messageLoopThread.SetApartmentState(ApartmentState.STA);
            _messageLoopThread.Start();
        }

        /// <summary>
        /// Register the F1 global hotkey
        /// </summary>
        public bool RegisterF1Hotkey()
        {
            // Wait a moment for the form to be created
            Thread.Sleep(100);

            if (_form == null)
            {
                Console.WriteLine("⚠ Cannot register F1 hotkey - form not created yet");
                return false;
            }

            try
            {
                Console.WriteLine($"Attempting to register F1 hotkey with window handle: {_form.Handle}");
                bool success = RegisterHotKey(_form.Handle, _hotkeyId, MOD_NONE, VK_F1);
                if (success)
                {
                    Console.WriteLine("✓ Global F1 hotkey registered successfully");
                    Console.WriteLine("  Press F1 anywhere to activate voice commands");
                }
                else
                {
                    var lastError = Marshal.GetLastWin32Error();
                    Console.WriteLine($"⚠ Failed to register F1 hotkey (Error code: {lastError})");
                    Console.WriteLine("  This may mean F1 is already in use by another application");
                }
                return success;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error registering F1 hotkey: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Unregister the F1 global hotkey
        /// </summary>
        public void UnregisterF1Hotkey()
        {
            try
            {
                if (_form != null)
                {
                    UnregisterHotKey(_form.Handle, _hotkeyId);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error unregistering F1 hotkey: {ex.Message}");
            }
        }

        /// <summary>
        /// Message loop worker that runs in a separate thread to handle Windows messages
        /// </summary>
        private void MessageLoopWorker()
        {
            try
            {
                Console.WriteLine("Starting hotkey message loop thread...");

                // Create a message-only window for receiving hotkey messages
                Application.EnableVisualStyles();
                _form = new HotkeyForm(this);

                Console.WriteLine("Created hotkey form, starting message loop...");

                // Run the message loop until stop is requested
                while (!_stopEvent.IsSet)
                {
                    Application.DoEvents();
                    Thread.Sleep(10); // Small delay to prevent high CPU usage
                }

                Console.WriteLine("Message loop stopping...");
                _form.Dispose();
                _form = null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in hotkey message loop: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// Handle hotkey message
        /// </summary>
        internal void OnHotkeyPressed(int hotkeyId)
        {
            Console.WriteLine($"Hotkey message received with ID: {hotkeyId} (expected: {_hotkeyId})");
            if (hotkeyId == _hotkeyId)
            {
                Console.WriteLine("F1 hotkey matched - firing event");
                F1Pressed?.Invoke(this, EventArgs.Empty);
            }
        }

        /// <summary>
        /// Dispose resources and unregister hotkey
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                UnregisterF1Hotkey();
                _stopEvent.Set();
                
                if (_messageLoopThread != null && _messageLoopThread.IsAlive)
                {
                    _messageLoopThread.Join(1000); // Wait up to 1 second for thread to finish
                }

                _stopEvent?.Dispose();
                _disposed = true;
            }
        }

        /// <summary>
        /// Hidden form for receiving Windows messages
        /// </summary>
        private class HotkeyForm : Form
        {
            private readonly GlobalHotkeyService _service;

            public HotkeyForm(GlobalHotkeyService service)
            {
                _service = service;
                
                // Make the form invisible and non-interactive
                this.WindowState = FormWindowState.Minimized;
                this.ShowInTaskbar = false;
                this.Visible = false;
                this.FormBorderStyle = FormBorderStyle.None;
                this.Size = new System.Drawing.Size(0, 0);
            }

            /// <summary>
            /// Override WndProc to handle hotkey messages
            /// </summary>
            protected override void WndProc(ref Message m)
            {
                if (m.Msg == WM_HOTKEY)
                {
                    int hotkeyId = m.WParam.ToInt32();
                    Console.WriteLine($"WndProc received WM_HOTKEY message with ID: {hotkeyId}");
                    _service.OnHotkeyPressed(hotkeyId);
                }
                else
                {
                    base.WndProc(ref m);
                }
            }
        }
    }
}

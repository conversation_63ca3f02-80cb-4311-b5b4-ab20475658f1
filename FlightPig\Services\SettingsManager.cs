using System;
using System.IO;
using Newtonsoft.Json;
using FlightPig.Models;

namespace FlightPig.Services
{
    /// <summary>
    /// Manages application settings and configuration
    /// </summary>
    public class SettingsManager
    {
        private readonly string _settingsPath;
        private AppSettings _settings;

        public SettingsManager(string settingsPath = "settings.json")
        {
            _settingsPath = settingsPath;
            _settings = LoadSettings();
        }

        public AppSettings Settings => _settings;

        /// <summary>
        /// Load settings from file or create default settings
        /// </summary>
        private AppSettings LoadSettings()
        {
            try
            {
                if (File.Exists(_settingsPath))
                {
                    var json = File.ReadAllText(_settingsPath);
                    var settings = JsonConvert.DeserializeObject<AppSettings>(json);
                    if (settings != null)
                    {
                        Console.WriteLine("Settings loaded from file.");
                        return settings;
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading settings: {ex.Message}");
            }

            Console.WriteLine("Creating default settings...");
            var defaultSettings = CreateDefaultSettings();
            SaveSettings(defaultSettings);
            return defaultSettings;
        }

        /// <summary>
        /// Create default settings with example values
        /// </summary>
        private AppSettings CreateDefaultSettings()
        {
            return new AppSettings
            {
                Ollama = new OllamaSettings
                {
                    BaseUrl = "http://localhost:11434",
                    ModelName = "deepseek-r1:8b",
                    TimeoutMinutes = 30
                },
                Whisper = new WhisperSettings
                {
                    ModelName = "ggml-tiny.bin",
                    Language = "en",
                    RecordingTimeoutSeconds = 10,
                    SilenceThresholdMs = 2000,
                    Enabled = true
                },
                ElevenLabs = new ElevenLabsSettings
                {
                    ApiKey = "YOUR_ELEVENLABS_API_KEY_HERE",
                    BaseUrl = "https://api.elevenlabs.io/v1",
                    DefaultVoiceId = "21m00Tcm4TlvDq8ikWAM", // Rachel - calm, professional
                    PilotVoiceId = "29vD33N1CtxCmqQRPOHJ", // Drew - authoritative, clear
                    Enabled = false, // Disabled by default until API key is set
                    Stability = 0.5,
                    SimilarityBoost = 0.5
                },
                Voice = new VoiceSettings
                {
                    PushToTalkKey = "Space",
                    VoiceActivation = true,
                    ContinuousListening = false,
                    ConfirmationSounds = true,
                    Volume = 0.8
                }
            };
        }

        /// <summary>
        /// Save settings to file
        /// </summary>
        public void SaveSettings(AppSettings settings = null)
        {
            try
            {
                var settingsToSave = settings ?? _settings;
                var json = JsonConvert.SerializeObject(settingsToSave, Formatting.Indented);
                File.WriteAllText(_settingsPath, json);
                Console.WriteLine($"Settings saved to {_settingsPath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving settings: {ex.Message}");
            }
        }

        /// <summary>
        /// Update settings and save to file
        /// </summary>
        public void UpdateSettings(AppSettings newSettings)
        {
            _settings = newSettings;
            SaveSettings();
        }

        /// <summary>
        /// Check if ElevenLabs is properly configured
        /// </summary>
        public bool IsElevenLabsConfigured()
        {
            return !string.IsNullOrEmpty(_settings.ElevenLabs.ApiKey) && 
                   _settings.ElevenLabs.ApiKey != "YOUR_ELEVENLABS_API_KEY_HERE" &&
                   _settings.ElevenLabs.Enabled;
        }

        /// <summary>
        /// Check if voice features are enabled
        /// </summary>
        public bool IsVoiceEnabled()
        {
            return _settings.Whisper.Enabled;
        }

        /// <summary>
        /// Display current settings summary
        /// </summary>
        public void DisplaySettings()
        {
            Console.WriteLine("=== CURRENT SETTINGS ===");
            Console.WriteLine($"Ollama URL: {_settings.Ollama.BaseUrl}");
            Console.WriteLine($"Ollama Model: {_settings.Ollama.ModelName}");
            Console.WriteLine($"Whisper Enabled: {_settings.Whisper.Enabled}");
            Console.WriteLine($"Whisper Model: {_settings.Whisper.ModelName}");
            Console.WriteLine($"ElevenLabs Enabled: {_settings.ElevenLabs.Enabled}");
            Console.WriteLine($"ElevenLabs Configured: {IsElevenLabsConfigured()}");
            Console.WriteLine($"Voice Activation: {_settings.Voice.VoiceActivation}");
            Console.WriteLine($"Settings File: {Path.GetFullPath(_settingsPath)}");
            Console.WriteLine();
        }
    }
}
